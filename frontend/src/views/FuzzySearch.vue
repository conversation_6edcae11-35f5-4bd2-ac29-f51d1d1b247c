<template>
  <div class="px-4 sm:px-0">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Fuzzy Parts Search</h1>
      <p class="mt-2 text-gray-600">Search for parts using advanced fuzzy matching powered by Elasticsearch</p>
    </div>

    <!-- Quick Search Form -->
    <div class="bg-white shadow rounded-lg p-6 mb-6">
      <form @submit.prevent="handleSearch" class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Main Search -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700">Part Number / Description</label>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Enter MPN, description, or manufacturer..."
              class="mt-1 input"
              :disabled="searching"
            />
          </div>

          <!-- Fuzzy Level -->
          <div>
            <label class="block text-sm font-medium text-gray-700">Fuzzy Level</label>
            <select
              v-model="fuzziness"
              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm"
              :disabled="searching"
            >
              <option value="0">Exact Match</option>
              <option value="1">1 Character Diff</option>
              <option value="2">2 Character Diff</option>
              <option value="AUTO">Auto (Recommended)</option>
            </select>
          </div>
        </div>

        <!-- Advanced Options -->
        <div class="border-t pt-4">
          <button
            type="button"
            @click="showAdvanced = !showAdvanced"
            class="text-sm text-gray-600 hover:text-gray-800 flex items-center"
          >
            <svg class="w-4 h-4 mr-1 transform transition-transform" :class="{ 'rotate-90': showAdvanced }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            Advanced Options
          </button>

          <div v-show="showAdvanced" class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Minimum Score -->
            <div>
              <label class="block text-sm font-medium text-gray-700">Minimum Score</label>
              <select
                v-model="minScore"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm text-sm"
                :disabled="searching"
              >
                <option value="">Auto (Recommended)</option>
                <option value="0.1">Very Low (0.1)</option>
                <option value="0.2">Low (0.2)</option>
                <option value="0.3">Medium (0.3)</option>
                <option value="0.5">High (0.5)</option>
                <option value="0.8">Very High (0.8)</option>
              </select>
              <p class="mt-1 text-xs text-gray-500">Higher values return fewer but more relevant results</p>
            </div>
          </div>
        </div>

        <div class="flex space-x-3">
          <button
            type="submit"
            :disabled="!searchQuery.trim() || searching"
            class="btn btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="searching" class="flex items-center">
              <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Searching...
            </span>
            <span v-else>🔍 Fuzzy Search</span>
          </button>

          <button
            type="button"
            @click="clearSearch"
            :disabled="searching"
            class="btn btn-secondary disabled:opacity-50"
          >
            Clear
          </button>
        </div>
      </form>
    </div>

    <!-- Error Message -->
    <div v-if="error" class="mb-4 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
      {{ error }}
    </div>

    <!-- Search Results -->
    <div v-if="searchResults" class="bg-white shadow rounded-lg">
      <!-- Results Header -->
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <div>
            <h2 class="text-lg font-medium text-gray-900">
              Search Results
              <span class="text-sm font-normal text-gray-500">
                ({{ searchResults.pagination?.total || 0 }} parts found)
              </span>
            </h2>
            <!-- Search Type Indicator -->
            <div class="mt-1 flex items-center space-x-2">
              <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
                Elasticsearch Fuzzy Search
              </span>
              <span class="text-xs text-gray-500">
                Fuzziness: {{ fuzziness }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- Results Table -->
      <div v-if="searchResults.parts.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Part Number
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Manufacturer
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Quantity
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Price
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contact
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                List Type
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Score
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="part in searchResults.parts" :key="part.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">
                  <HighlightedText
                    :query="searchQuery"
                    :text="part.part_number"
                    mode="simple"
                    :show-similarity="true"
                  />
                </div>
              </td>
              <td class="px-6 py-4">
                <div class="text-sm text-gray-900 max-w-xs" :title="part.description">
                  <HighlightedText
                    v-if="part.description"
                    :query="searchQuery"
                    :text="part.description"
                    mode="substring"
                    :max-length="50"
                  />
                  <span v-else class="text-gray-400">-</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  <HighlightedText
                    v-if="part.manufacturer"
                    :query="searchQuery"
                    :text="part.manufacturer"
                    mode="substring"
                  />
                  <span v-else class="text-gray-400">-</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ part.quantity || '-' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  {{ part.unit_price ? `$${part.unit_price}` : '-' }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ part.contact_name }}</div>
                <div class="text-sm text-gray-500">{{ part.contact_company || '' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                      :class="{
                        'bg-green-100 text-green-800': part.list_type === 'offer',
                        'bg-blue-100 text-blue-800': part.list_type === 'needs',
                        'bg-gray-100 text-gray-800': part.list_type === 'using'
                      }">
                  {{ part.list_type }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  {{ part._score ? part._score.toFixed(2) : '-' }}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-12">
        <div class="text-gray-500">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No parts found</h3>
          <p class="mt-1 text-sm text-gray-500">Try adjusting your search criteria or fuzzy level.</p>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="searchResults.pagination && searchResults.pagination.pages > 1" class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            Showing {{ ((searchResults.pagination.page - 1) * searchResults.pagination.limit) + 1 }} to
            {{ Math.min(searchResults.pagination.page * searchResults.pagination.limit, searchResults.pagination.total) }}
            of {{ searchResults.pagination.total }} results
          </div>

          <div class="flex space-x-2">
            <button
              @click="goToPage(searchResults.pagination.page - 1)"
              :disabled="searchResults.pagination.page <= 1 || searching"
              class="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>

            <span class="px-3 py-1 text-sm">
              Page {{ searchResults.pagination.page }} of {{ searchResults.pagination.pages }}
            </span>

            <button
              @click="goToPage(searchResults.pagination.page + 1)"
              :disabled="searchResults.pagination.page >= searchResults.pagination.pages || searching"
              class="px-3 py-1 text-sm border rounded disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Fuzzy Search Tips -->
    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 class="text-sm font-medium text-blue-800 mb-2">🔍 Improved Fuzzy Search Tips</h3>
      <ul class="text-sm text-blue-700 space-y-1">
        <li><strong>Exact Match (0):</strong> Only finds exact matches</li>
        <li><strong>1 Character Diff:</strong> Allows 1 character difference (e.g., "ABC123" finds "ABC124")</li>
        <li><strong>2 Character Diff:</strong> Allows 2 character differences (e.g., "ABC123" finds "ABD124")</li>
        <li><strong>Auto (Recommended):</strong> Automatically adjusts fuzziness based on term length</li>
        <li><strong>Minimum Score:</strong> Filters out weak matches - higher values return fewer but more relevant results</li>
        <li><strong>Smart Matching:</strong> Prioritizes exact matches, prefix matches, and considers part number length</li>
      </ul>

      <div class="mt-3 pt-3 border-t border-blue-300">
        <h4 class="text-sm font-medium text-blue-800 mb-2">🎨 Color Highlighting Legend</h4>
        <div class="flex flex-wrap gap-3 text-xs">
          <span class="flex items-center">
            <span class="bg-green-200 text-green-900 px-1 rounded font-semibold mr-1">A</span>
            Exact match
          </span>
          <span class="flex items-center">
            <span class="bg-yellow-200 text-yellow-900 px-1 rounded font-semibold mr-1">B</span>
            Different character
          </span>
          <span class="flex items-center">
            <span class="bg-gray-100 text-gray-600 px-1 rounded mr-1">C</span>
            Extra character
          </span>
          <span class="text-blue-700">
            Similarity % shown for part numbers
          </span>
        </div>
      </div>
    </div>

    <!-- Highlighting Demo -->
    <HighlightingDemo />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { partsService, type PartsSearchParams, type PartsSearchResponse } from '@/services/parts'
import HighlightedText from '@/components/HighlightedText.vue'
import HighlightingDemo from '@/components/HighlightingDemo.vue'

const searching = ref(false)
const error = ref<string | null>(null)
const searchResults = ref<PartsSearchResponse | null>(null)
const searchQuery = ref('')
const fuzziness = ref('AUTO')
const minScore = ref('')
const showAdvanced = ref(false)
const currentPage = ref(1)

const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    error.value = 'Please enter a search query'
    return
  }

  try {
    searching.value = true
    error.value = null
    currentPage.value = 1

    const params: PartsSearchParams = {
      search: searchQuery.value.trim(),
      fuzziness: fuzziness.value,
      page: currentPage.value,
      limit: 20,
      min_score: minScore.value ? Number(minScore.value) : undefined
    }

    const result = await partsService.searchParts(params)
    searchResults.value = result

  } catch (err: any) {
    console.error('Search failed:', err)
    error.value = 'Search failed. Please try again.'
    searchResults.value = null
  } finally {
    searching.value = false
  }
}

const clearSearch = () => {
  searchQuery.value = ''
  fuzziness.value = 'AUTO'
  minScore.value = ''
  showAdvanced.value = false
  currentPage.value = 1
  searchResults.value = null
  error.value = null
}

const goToPage = async (page: number) => {
  currentPage.value = page

  try {
    searching.value = true

    const params: PartsSearchParams = {
      search: searchQuery.value.trim(),
      fuzziness: fuzziness.value,
      page: currentPage.value,
      limit: 20,
      min_score: minScore.value ? Number(minScore.value) : undefined
    }

    const result = await partsService.searchParts(params)
    searchResults.value = result

  } catch (err: any) {
    console.error('Search failed:', err)
    error.value = 'Search failed. Please try again.'
  } finally {
    searching.value = false
  }
}
</script>

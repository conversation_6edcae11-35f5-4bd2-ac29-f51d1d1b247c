import { computed } from 'vue'

export interface HighlightedChar {
  char: string
  type: 'match' | 'diff' | 'extra' | 'missing'
  index: number
}

export interface HighlightResult {
  chars: HighlightedChar[]
  similarity: number
}

/**
 * Composable for highlighting differences between search query and result text
 */
export function useTextHighlighting() {
  
  /**
   * Calculate Levenshtein distance between two strings
   */
  function levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))
    
    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j
    
    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        )
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  /**
   * Calculate similarity percentage between two strings
   */
  function calculateSimilarity(query: string, text: string): number {
    if (!query || !text) return 0
    
    const maxLength = Math.max(query.length, text.length)
    if (maxLength === 0) return 100
    
    const distance = levenshteinDistance(query.toLowerCase(), text.toLowerCase())
    return Math.max(0, (maxLength - distance) / maxLength * 100)
  }

  /**
   * Highlight differences between query and text using dynamic programming alignment
   */
  function highlightDifferences(query: string, text: string): HighlightResult {
    if (!query || !text) {
      return {
        chars: text.split('').map((char, index) => ({
          char,
          type: 'extra' as const,
          index
        })),
        similarity: 0
      }
    }

    const q = query.toLowerCase()
    const t = text.toLowerCase()
    
    // Create alignment matrix for optimal character matching
    const matrix = Array(t.length + 1).fill(null).map(() => 
      Array(q.length + 1).fill(null).map(() => ({ cost: 0, path: '' }))
    )
    
    // Initialize base cases
    for (let i = 0; i <= q.length; i++) {
      matrix[0][i] = { cost: i, path: 'D'.repeat(i) }
    }
    for (let j = 0; j <= t.length; j++) {
      matrix[j][0] = { cost: j, path: 'I'.repeat(j) }
    }
    
    // Fill the matrix
    for (let j = 1; j <= t.length; j++) {
      for (let i = 1; i <= q.length; i++) {
        const match = q[i - 1] === t[j - 1] ? 0 : 1
        const costs = [
          { cost: matrix[j][i - 1].cost + 1, op: 'D' },      // deletion
          { cost: matrix[j - 1][i].cost + 1, op: 'I' },      // insertion
          { cost: matrix[j - 1][i - 1].cost + match, op: match ? 'S' : 'M' } // substitution/match
        ]
        
        const best = costs.reduce((min, curr) => curr.cost < min.cost ? curr : min)
        matrix[j][i] = {
          cost: best.cost,
          path: matrix[j - (best.op === 'I' ? 1 : 0)][i - (best.op === 'D' ? 1 : 0)].path + best.op
        }
      }
    }
    
    // Trace back the optimal alignment
    const operations = matrix[t.length][q.length].path
    const chars: HighlightedChar[] = []
    let textIndex = 0
    let queryIndex = 0
    
    for (const op of operations) {
      switch (op) {
        case 'M': // Match
          chars.push({
            char: text[textIndex],
            type: 'match',
            index: textIndex
          })
          textIndex++
          queryIndex++
          break
          
        case 'S': // Substitution (difference)
          chars.push({
            char: text[textIndex],
            type: 'diff',
            index: textIndex
          })
          textIndex++
          queryIndex++
          break
          
        case 'I': // Insertion (extra character in text)
          chars.push({
            char: text[textIndex],
            type: 'extra',
            index: textIndex
          })
          textIndex++
          break
          
        case 'D': // Deletion (missing character from query)
          // We don't add missing characters to the result since we're highlighting the text
          queryIndex++
          break
      }
    }
    
    // Add any remaining characters as extra
    while (textIndex < text.length) {
      chars.push({
        char: text[textIndex],
        type: 'extra',
        index: textIndex
      })
      textIndex++
    }
    
    const similarity = calculateSimilarity(query, text)
    
    return { chars, similarity }
  }

  /**
   * Create a simple case-insensitive highlight for exact substring matches
   */
  function highlightSubstring(query: string, text: string): HighlightResult {
    if (!query || !text) {
      return {
        chars: text.split('').map((char, index) => ({
          char,
          type: 'extra' as const,
          index
        })),
        similarity: 0
      }
    }

    const lowerQuery = query.toLowerCase()
    const lowerText = text.toLowerCase()
    const chars: HighlightedChar[] = []
    
    let textIndex = 0
    while (textIndex < text.length) {
      const remainingText = lowerText.slice(textIndex)
      const matchIndex = remainingText.indexOf(lowerQuery)
      
      if (matchIndex === 0) {
        // Found a match at current position
        for (let i = 0; i < query.length; i++) {
          chars.push({
            char: text[textIndex + i],
            type: 'match',
            index: textIndex + i
          })
        }
        textIndex += query.length
      } else {
        // No match, mark as extra
        chars.push({
          char: text[textIndex],
          type: 'extra',
          index: textIndex
        })
        textIndex++
      }
    }
    
    const similarity = calculateSimilarity(query, text)
    return { chars, similarity }
  }

  /**
   * Get CSS classes for different highlight types
   */
  function getHighlightClasses(type: HighlightedChar['type']): string {
    switch (type) {
      case 'match':
        return 'bg-green-200 text-green-900 font-semibold'
      case 'diff':
        return 'bg-yellow-200 text-yellow-900 font-semibold'
      case 'extra':
        return 'bg-gray-100 text-gray-600'
      case 'missing':
        return 'bg-red-200 text-red-900 font-semibold'
      default:
        return ''
    }
  }

  return {
    highlightDifferences,
    highlightSubstring,
    calculateSimilarity,
    getHighlightClasses
  }
}

import { esClient } from '../config/database';

export interface ElasticsearchPart {
  id: string;
  list_id: string;
  part_number: string;
  description?: string;
  manufacturer?: string;
  quantity?: number;
  unit_price?: number;
  condition?: string;
  list_type: string;
  contact_id: string;
  contact_name: string;
  contact_company?: string;
  created_at: string;
}

export interface FuzzySearchParams {
  query: string;
  manufacturer?: string;
  list_type?: 'offer' | 'needs' | 'using';
  contact_id?: string;
  min_price?: number;
  max_price?: number;
  condition?: string;
  page?: number;
  limit?: number;
  fuzziness?: string; // 'AUTO', '0', '1', '2'
  min_score?: number; // Minimum relevance score threshold
}

export interface SearchResult {
  parts: ElasticsearchPart[];
  total: number;
  page: number;
  limit: number;
  pages: number;
  aggregations?: {
    manufacturers: Array<{ key: string; doc_count: number }>;
    list_types: Array<{ key: string; doc_count: number }>;
    conditions: Array<{ key: string; doc_count: number }>;
  };
}

export class ElasticsearchService {

  // Parse query to extract potential manufacturer and part number
  private parseQuery(query: string): { manufacturer?: string; partNumber?: string; originalQuery: string } {
    const trimmed = query.trim();
    const words = trimmed.split(/\s+/);

    // Common manufacturer patterns (add more as needed)
    const knownManufacturers = [
      'MURATA', 'AVX', 'SAMSUNG', 'YAGEO', 'VISHAY', 'KEMET', 'TDK', 'PANASONIC',
      'NICHICON', 'RUBYCON', 'CORNELL', 'DUBILIER', 'EPCOS', 'INFINEON', 'ST',
      'TEXAS', 'INSTRUMENTS', 'ANALOG', 'DEVICES', 'MAXIM', 'LINEAR', 'TECHNOLOGY',
      'MICROCHIP', 'ATMEL', 'NXP', 'FREESCALE', 'CYPRESS', 'XILINX', 'ALTERA',
      'INTEL', 'AMD', 'NVIDIA', 'BROADCOM', 'QUALCOMM', 'MARVELL', 'REALTEK',
      'ROHM', 'TOSHIBA', 'RENESAS', 'FUJITSU', 'HITACHI', 'MITSUBISHI', 'SONY',
      'KYOCERA', 'CITIZEN', 'EPSON', 'SEIKO', 'ABRACON', 'CRYSTEK', 'IQXO',
      'SII', 'SITIME', 'VECTRON', 'CONNOR', 'WINBOND', 'MACRONIX', 'SPANSION',
      'MICRON', 'HYNIX', 'SAMSUNG', 'KINGSTON', 'CORSAIR', 'GSKILL', 'CRUCIAL'
    ];

    let manufacturer: string | undefined;
    let partNumber: string | undefined;

    if (words.length >= 2) {
      // Check if first word is a known manufacturer
      const firstWord = words[0].toUpperCase();
      const isKnownManufacturer = knownManufacturers.some(mfg =>
        firstWord === mfg || firstWord.includes(mfg) || mfg.includes(firstWord)
      );

      if (isKnownManufacturer) {
        manufacturer = words[0];
        partNumber = words.slice(1).join(' ');
      } else {
        // Check if any word looks like a manufacturer
        for (let i = 0; i < words.length; i++) {
          const word = words[i].toUpperCase();
          if (knownManufacturers.some(mfg => word === mfg || word.includes(mfg) || mfg.includes(word))) {
            manufacturer = words[i];
            // Rest of the words form the part number
            partNumber = [...words.slice(0, i), ...words.slice(i + 1)].join(' ');
            break;
          }
        }
      }
    }

    // If we couldn't parse it, treat as part number search
    if (!manufacturer && !partNumber) {
      partNumber = trimmed;
    }

    return {
      manufacturer,
      partNumber,
      originalQuery: trimmed
    };
  }

  // Index a single part
  async indexPart(part: ElasticsearchPart): Promise<void> {
    try {
      await esClient.index({
        index: 'parts',
        id: part.id,
        body: part
      });
    } catch (error) {
      console.error('Error indexing part:', error);
      throw error;
    }
  }

  // Index multiple parts in bulk
  async bulkIndexParts(parts: ElasticsearchPart[]): Promise<void> {
    if (parts.length === 0) return;

    try {
      const body = parts.flatMap(part => [
        { index: { _index: 'parts', _id: part.id } },
        part
      ]);

      const response = await esClient.bulk({ body });

      if (response.errors) {
        console.error('Bulk indexing errors:', response.items.filter(item => item.index?.error));
      }
    } catch (error) {
      console.error('Error bulk indexing parts:', error);
      throw error;
    }
  }

  // Fuzzy search for parts
  async fuzzySearch(params: FuzzySearchParams): Promise<SearchResult> {
    const {
      query,
      manufacturer,
      list_type,
      contact_id,
      min_price,
      max_price,
      condition,
      page = 1,
      limit = 20,
      fuzziness = 'AUTO',
      min_score
    } = params;

    const from = (page - 1) * limit;

    // Build the search query
    const searchBody: any = {
      query: {
        bool: {
          must: [],
          filter: []
        }
      },
      sort: [
        { _score: { order: 'desc' } },
        { 'part_number.keyword': { order: 'asc' } }
      ],
      from,
      size: limit,
      highlight: {
        fields: {
          part_number: {},
          'part_number.ngram': {},
          description: {},
          'description.ngram': {},
          manufacturer: {}
        }
      },
      aggs: {
        manufacturers: {
          terms: { field: 'manufacturer.keyword', size: 20 }
        },
        list_types: {
          terms: { field: 'list_type', size: 10 }
        },
        conditions: {
          terms: { field: 'condition', size: 10 }
        }
      }
    };

    // Main search query with improved fuzzy matching
    if (query && query.trim()) {
      const queryLength = query.trim().length;

      // Parse the query to extract manufacturer and part number
      const parsedQuery = this.parseQuery(query.trim());
      const { manufacturer: parsedMfg, partNumber: parsedPart, originalQuery } = parsedQuery;

      // Calculate dynamic minimum score based on query length and fuzziness
      let dynamicMinScore = 0.1; // Default minimum
      if (fuzziness === '0') {
        dynamicMinScore = 0.8; // Exact matches should have high scores
      } else if (fuzziness === '1') {
        dynamicMinScore = 0.5; // 1 char diff
      } else if (fuzziness === '2') {
        dynamicMinScore = 0.3; // 2 char diff
      } else {
        dynamicMinScore = 0.2; // AUTO fuzziness
      }

      const shouldQueries: any[] = [];

      // If we have a parsed manufacturer and part number, create targeted searches
      if (parsedMfg && parsedPart) {
        // MULTI-FIELD SEARCH: Both manufacturer and part number identified

        // 1. Perfect match: both manufacturer and part number match exactly (HIGHEST SCORE)
        shouldQueries.push({
          bool: {
            must: [
              {
                term: {
                  'manufacturer.keyword': {
                    value: parsedMfg.toUpperCase(),
                    boost: 1
                  }
                }
              },
              {
                term: {
                  'part_number.keyword': {
                    value: parsedPart.toUpperCase(),
                    boost: 1
                  }
                }
              }
            ],
            boost: 50 // Massive boost for perfect matches
          }
        });

        // 2. Manufacturer exact + Part number fuzzy
        shouldQueries.push({
          bool: {
            must: [
              {
                term: {
                  'manufacturer.keyword': {
                    value: parsedMfg.toUpperCase(),
                    boost: 1
                  }
                }
              },
              {
                fuzzy: {
                  part_number: {
                    value: parsedPart,
                    fuzziness: fuzziness,
                    boost: 1
                  }
                }
              }
            ],
            boost: 35
          }
        });

        // 3. Manufacturer fuzzy + Part number exact
        shouldQueries.push({
          bool: {
            must: [
              {
                fuzzy: {
                  manufacturer: {
                    value: parsedMfg,
                    fuzziness: fuzziness,
                    boost: 1
                  }
                }
              },
              {
                term: {
                  'part_number.keyword': {
                    value: parsedPart.toUpperCase(),
                    boost: 1
                  }
                }
              }
            ],
            boost: 30
          }
        });

        // 4. Both manufacturer and part number fuzzy
        shouldQueries.push({
          bool: {
            must: [
              {
                fuzzy: {
                  manufacturer: {
                    value: parsedMfg,
                    fuzziness: fuzziness,
                    boost: 1
                  }
                }
              },
              {
                fuzzy: {
                  part_number: {
                    value: parsedPart,
                    fuzziness: fuzziness,
                    boost: 1
                  }
                }
              }
            ],
            boost: 25
          }
        });

        // 5. Individual field matches (lower priority)
        // Manufacturer matches
        shouldQueries.push(
          {
            term: {
              'manufacturer.keyword': {
                value: parsedMfg.toUpperCase(),
                boost: 15
              }
            }
          },
          {
            fuzzy: {
              manufacturer: {
                value: parsedMfg,
                fuzziness: fuzziness,
                boost: 12,
                max_expansions: 50
              }
            }
          }
        );

        // Part number matches
        shouldQueries.push(
          {
            term: {
              'part_number.keyword': {
                value: parsedPart.toUpperCase(),
                boost: 20
              }
            }
          },
          {
            prefix: {
              'part_number.keyword': {
                value: parsedPart.toUpperCase(),
                boost: 18
              }
            }
          },
          {
            fuzzy: {
              part_number: {
                value: parsedPart,
                fuzziness: fuzziness,
                boost: 15,
                max_expansions: 50,
                prefix_length: Math.min(2, Math.floor(parsedPart.length / 3))
              }
            }
          }
        );
      }

      // FALLBACK: Original query searches (for when parsing doesn't work or single terms)
      const searchTerm = parsedPart || originalQuery;
      const searchLength = searchTerm.length;

      // Part number searches with original query
      shouldQueries.push(
        // Exact match (highest score)
        {
          term: {
            'part_number.keyword': {
              value: searchTerm.toUpperCase(),
              boost: parsedMfg ? 10 : 15 // Lower boost if we already have targeted searches
            }
          }
        },
        // Prefix match
        {
          prefix: {
            'part_number.keyword': {
              value: searchTerm.toUpperCase(),
              boost: parsedMfg ? 8 : 12
            }
          }
        },
        // Fuzzy match on part number
        {
          fuzzy: {
            part_number: {
              value: searchTerm,
              fuzziness: fuzziness,
              boost: searchLength > 6 ? (parsedMfg ? 6 : 10) : (parsedMfg ? 4 : 8),
              max_expansions: 50,
              prefix_length: Math.min(2, Math.floor(searchLength / 3))
            }
          }
        },
        // Wildcard search
        {
          wildcard: {
            'part_number.keyword': {
              value: `*${searchTerm.toUpperCase()}*`,
              boost: searchLength > 4 ? (parsedMfg ? 3 : 7) : (parsedMfg ? 2 : 4)
            }
          }
        }
      );

      // N-gram match on part number (only for longer queries)
      if (searchLength > 4) {
        shouldQueries.push({
          match: {
            'part_number.ngram': {
              query: searchTerm,
              boost: parsedMfg ? 3 : 5,
              minimum_should_match: '70%'
            }
          }
        });
      }

      // Manufacturer searches (if not already parsed)
      if (!parsedMfg) {
        shouldQueries.push(
          {
            term: {
              'manufacturer.keyword': {
                value: originalQuery.toUpperCase(),
                boost: 11
              }
            }
          },
          {
            prefix: {
              'manufacturer.keyword': {
                value: originalQuery.toUpperCase(),
                boost: 9
              }
            }
          },
          {
            fuzzy: {
              manufacturer: {
                value: originalQuery,
                fuzziness: fuzziness,
                boost: 7,
                max_expansions: 50
              }
            }
          },
          {
            wildcard: {
              'manufacturer.keyword': {
                value: `*${originalQuery.toUpperCase()}*`,
                boost: 6
              }
            }
          }
        );

        // Manufacturer n-gram match
        if (queryLength > 2) {
          shouldQueries.push({
            match: {
              'manufacturer.ngram': {
                query: originalQuery,
                boost: 5,
                minimum_should_match: '60%'
              }
            }
          });
        }
      }

      // Description search (always lower priority)
      shouldQueries.push({
        fuzzy: {
          description: {
            value: originalQuery,
            fuzziness: fuzziness,
            boost: 3,
            max_expansions: 25
          }
        }
      });

      searchBody.query.bool.must.push({
        bool: {
          should: shouldQueries,
          minimum_should_match: 1
        }
      });

      // Apply minimum score if specified or use dynamic minimum
      const effectiveMinScore = min_score || dynamicMinScore;
      if (effectiveMinScore > 0) {
        searchBody.min_score = effectiveMinScore;
      }
    } else {
      // If no query, match all
      searchBody.query.bool.must.push({ match_all: {} });
    }

    // Add filters
    if (manufacturer) {
      searchBody.query.bool.filter.push({
        term: { 'manufacturer.keyword': manufacturer }
      });
    }

    if (list_type) {
      searchBody.query.bool.filter.push({
        term: { list_type: list_type }
      });
    }

    if (contact_id) {
      searchBody.query.bool.filter.push({
        term: { contact_id: contact_id }
      });
    }

    if (condition) {
      searchBody.query.bool.filter.push({
        term: { condition: condition }
      });
    }

    // Price range filter
    if (min_price !== undefined || max_price !== undefined) {
      const priceFilter: any = { range: { unit_price: {} } };
      if (min_price !== undefined) priceFilter.range.unit_price.gte = min_price;
      if (max_price !== undefined) priceFilter.range.unit_price.lte = max_price;
      searchBody.query.bool.filter.push(priceFilter);
    }

    try {
      const response = await esClient.search({
        index: 'parts',
        body: searchBody
      });

      const parts = response.hits.hits.map((hit: any) => ({
        ...hit._source,
        _score: hit._score,
        _highlights: hit.highlight
      }));

      const total = typeof response.hits.total === 'object'
        ? response.hits.total.value || 0
        : response.hits.total || 0;

      return {
        parts,
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
        aggregations: {
          manufacturers: (response.aggregations?.manufacturers as any)?.buckets || [],
          list_types: (response.aggregations?.list_types as any)?.buckets || [],
          conditions: (response.aggregations?.conditions as any)?.buckets || []
        }
      };
    } catch (error) {
      console.error('Elasticsearch search error:', error);
      throw error;
    }
  }

  // Delete a part from the index
  async deletePart(partId: string): Promise<void> {
    try {
      await esClient.delete({
        index: 'parts',
        id: partId
      });
    } catch (error: any) {
      if (error.statusCode !== 404) {
        console.error('Error deleting part from index:', error);
        throw error;
      }
    }
  }

  // Delete all parts for a specific list
  async deletePartsByList(listId: string): Promise<void> {
    try {
      await esClient.deleteByQuery({
        index: 'parts',
        body: {
          query: {
            term: { list_id: listId }
          }
        }
      });
    } catch (error) {
      console.error('Error deleting parts by list:', error);
      throw error;
    }
  }

  // Refresh the index
  async refreshIndex(): Promise<void> {
    try {
      await esClient.indices.refresh({ index: 'parts' });
    } catch (error) {
      console.error('Error refreshing index:', error);
      throw error;
    }
  }
}

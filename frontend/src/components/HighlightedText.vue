<template>
  <span class="highlighted-text">
    <span
      v-for="(charData, index) in highlightedChars"
      :key="index"
      :class="getHighlightClasses(charData.type)"
      :title="getTooltip(charData.type)"
      class="transition-colors duration-150"
    >{{ charData.char }}</span>

    <!-- Similarity indicator -->
    <span
      v-if="showSimilarity && similarity !== null"
      class="ml-2 text-xs text-gray-500 font-normal"
      :title="`${similarity.toFixed(1)}% similarity to search query`"
    >
      ({{ similarity.toFixed(0) }}%)
    </span>
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useTextHighlighting, type HighlightedChar } from '@/composables/useTextHighlighting'

interface Props {
  query: string
  text: string
  mode?: 'simple' | 'fuzzy' | 'substring'
  showSimilarity?: boolean
  maxLength?: number
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'simple',
  showSimilarity: false,
  maxLength: 100
})

const { highlightDifferences, highlightFuzzyDifferences, highlightSubstring, getHighlightClasses } = useTextHighlighting()

const highlightResult = computed(() => {
  if (!props.text) return { chars: [], similarity: 0 }

  const truncatedText = props.text.length > props.maxLength
    ? props.text.slice(0, props.maxLength) + '...'
    : props.text

  switch (props.mode) {
    case 'substring':
      return highlightSubstring(props.query, truncatedText)
    case 'fuzzy':
      return highlightFuzzyDifferences(props.query, truncatedText)
    case 'simple':
    default:
      return highlightDifferences(props.query, truncatedText)
  }
})

const highlightedChars = computed(() => highlightResult.value.chars)
const similarity = computed(() => props.showSimilarity ? highlightResult.value.similarity : null)

function getTooltip(type: HighlightedChar['type']): string {
  switch (type) {
    case 'match':
      return 'Exact match with search query'
    case 'diff':
      return 'Different character (substitution)'
    case 'extra':
      return 'Extra character not in search query'
    case 'missing':
      return 'Missing character from search query'
    default:
      return ''
  }
}
</script>

<style scoped>
.highlighted-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  letter-spacing: 0.5px;
}

/* Hover effects for better UX */
.highlighted-text span:hover {
  transform: scale(1.05);
  z-index: 10;
  position: relative;
}
</style>

#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to reindex Elasticsearch with improved settings
 * This script will:
 * 1. Delete the existing parts index
 * 2. Recreate it with improved n-gram settings
 * 3. Reindex all parts from PostgreSQL
 */

const { Client } = require('@elastic/elasticsearch');
const { Pool } = require('pg');

// Configuration
const esClient = new Client({
  node: process.env.ES_NODE || 'http://localhost:9200',
});

const pgPool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'parts_broker',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
});

async function reindexElasticsearch() {
  try {
    console.log('🔄 Starting Elasticsearch reindexing...');

    // Step 1: Delete existing index
    console.log('🗑️  Deleting existing parts index...');
    try {
      await esClient.indices.delete({ index: 'parts' });
      console.log('✅ Existing index deleted');
    } catch (error) {
      if (error.meta?.statusCode === 404) {
        console.log('ℹ️  No existing index found');
      } else {
        throw error;
      }
    }

    // Step 2: Create new index with improved settings
    console.log('🏗️  Creating new index with improved settings...');
    await esClient.indices.create({
      index: 'parts',
      body: {
        mappings: {
          properties: {
            id: { type: 'keyword' },
            list_id: { type: 'keyword' },
            part_number: {
              type: 'text',
              analyzer: 'part_number_analyzer',
              fields: {
                keyword: { type: 'keyword' },
                ngram: {
                  type: 'text',
                  analyzer: 'ngram_analyzer'
                },
                fuzzy: {
                  type: 'text',
                  analyzer: 'fuzzy_analyzer'
                }
              }
            },
            description: {
              type: 'text',
              analyzer: 'fuzzy_analyzer',
              fields: {
                ngram: {
                  type: 'text',
                  analyzer: 'ngram_analyzer'
                }
              }
            },
            manufacturer: {
              type: 'text',
              analyzer: 'fuzzy_analyzer',
              fields: {
                keyword: { type: 'keyword' },
                ngram: {
                  type: 'text',
                  analyzer: 'ngram_analyzer'
                }
              }
            },
            quantity: { type: 'integer' },
            unit_price: { type: 'float' },
            condition: { type: 'keyword' },
            list_type: { type: 'keyword' },
            contact_id: { type: 'keyword' },
            contact_name: { type: 'text' },
            contact_company: { type: 'text' },
            created_at: { type: 'date' }
          }
        },
        settings: {
          analysis: {
            analyzer: {
              ngram_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'ngram_filter']
              },
              fuzzy_analyzer: {
                type: 'custom',
                tokenizer: 'standard',
                filter: ['lowercase', 'asciifolding', 'stop']
              },
              part_number_analyzer: {
                type: 'custom',
                tokenizer: 'keyword',
                filter: ['lowercase', 'asciifolding']
              }
            },
            filter: {
              ngram_filter: {
                type: 'ngram',
                min_gram: 3,
                max_gram: 6
              }
            }
          }
        }
      }
    });
    console.log('✅ New index created with improved settings');

    // Step 3: Fetch all parts from PostgreSQL
    console.log('📊 Fetching parts from PostgreSQL...');
    const query = `
      SELECT p.*, l.list_type, l.filename, c.name as contact_name, c.company as contact_company
      FROM parts p
      JOIN lists l ON p.list_id = l.id
      JOIN contacts c ON l.contact_id = c.id
      ORDER BY p.id
    `;
    
    const result = await pgPool.query(query);
    const parts = result.rows;
    console.log(`📦 Found ${parts.length} parts to index`);

    // Step 4: Bulk index parts
    if (parts.length > 0) {
      console.log('🔄 Bulk indexing parts...');
      const batchSize = 1000;
      let indexed = 0;

      for (let i = 0; i < parts.length; i += batchSize) {
        const batch = parts.slice(i, i + batchSize);
        const body = batch.flatMap(part => [
          { index: { _index: 'parts', _id: part.id } },
          {
            id: part.id,
            list_id: part.list_id,
            part_number: part.part_number,
            description: part.description,
            manufacturer: part.manufacturer,
            quantity: part.quantity,
            unit_price: part.unit_price,
            condition: part.condition,
            list_type: part.list_type,
            contact_id: part.contact_id,
            contact_name: part.contact_name,
            contact_company: part.contact_company,
            created_at: part.created_at
          }
        ]);

        const response = await esClient.bulk({ body });
        
        if (response.errors) {
          console.error('❌ Bulk indexing errors:', response.items.filter(item => item.index?.error));
        }

        indexed += batch.length;
        console.log(`📈 Indexed ${indexed}/${parts.length} parts (${Math.round(indexed/parts.length*100)}%)`);
      }
    }

    // Step 5: Refresh index
    console.log('🔄 Refreshing index...');
    await esClient.indices.refresh({ index: 'parts' });

    console.log('✅ Elasticsearch reindexing completed successfully!');
    console.log('🎉 The search should now be more precise and relevant');

  } catch (error) {
    console.error('❌ Reindexing failed:', error);
    process.exit(1);
  } finally {
    await pgPool.end();
  }
}

// Run the reindexing
reindexElasticsearch();

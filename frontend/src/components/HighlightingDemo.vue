<template>
  <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
    <h4 class="text-sm font-medium text-gray-800 mb-3">🎨 Highlighting Demo</h4>
    
    <div class="space-y-3">
      <!-- Example 1: Good fuzzy match -->
      <div class="bg-white p-3 rounded border">
        <div class="text-xs text-gray-500 mb-1">Search: "INN2XX4K" → Result:</div>
        <HighlightedText 
          query="INN2XX4K" 
          text="INN2904K" 
          mode="fuzzy"
          :show-similarity="true"
        />
      </div>
      
      <!-- Example 2: Partial match -->
      <div class="bg-white p-3 rounded border">
        <div class="text-xs text-gray-500 mb-1">Search: "STM32" → Result:</div>
        <HighlightedText 
          query="STM32" 
          text="STM32F407VGT6" 
          mode="fuzzy"
          :show-similarity="true"
        />
      </div>
      
      <!-- Example 3: Description match -->
      <div class="bg-white p-3 rounded border">
        <div class="text-xs text-gray-500 mb-1">Search: "microcontroller" → Description:</div>
        <HighlightedText 
          query="microcontroller" 
          text="32-bit ARM Cortex-M4 Microcontroller" 
          mode="substring"
        />
      </div>
    </div>
    
    <div class="mt-3 text-xs text-gray-600">
      Hover over highlighted characters to see tooltips explaining the match type.
    </div>
  </div>
</template>

<script setup lang="ts">
import HighlightedText from './HighlightedText.vue'
</script>

import { computed } from 'vue'

export interface HighlightedChar {
  char: string
  type: 'match' | 'diff' | 'extra' | 'missing'
  index: number
}

export interface HighlightResult {
  chars: HighlightedChar[]
  similarity: number
}

/**
 * Composable for highlighting differences between search query and result text
 */
export function useTextHighlighting() {

  /**
   * Calculate Levenshtein distance between two strings
   */
  function levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        )
      }
    }

    return matrix[str2.length][str1.length]
  }

  /**
   * Calculate similarity percentage between two strings
   */
  function calculateSimilarity(query: string, text: string): number {
    if (!query || !text) return 0

    const maxLength = Math.max(query.length, text.length)
    if (maxLength === 0) return 100

    const distance = levenshteinDistance(query.toLowerCase(), text.toLowerCase())
    return Math.max(0, (maxLength - distance) / maxLength * 100)
  }

  /**
   * Simple character-by-character comparison highlighting
   */
  function highlightDifferences(query: string, text: string): HighlightResult {
    if (!query || !text) {
      return {
        chars: text.split('').map((char, index) => ({
          char,
          type: 'extra' as const,
          index
        })),
        similarity: 0
      }
    }

    const q = query.toLowerCase()
    const t = text.toLowerCase()
    const chars: HighlightedChar[] = []

    const maxLength = Math.max(q.length, t.length)

    // Compare character by character
    for (let i = 0; i < maxLength; i++) {
      if (i < text.length) {
        const textChar = text[i]
        const queryChar = i < query.length ? query[i] : null

        if (queryChar === null) {
          // Text is longer than query - extra characters
          chars.push({
            char: textChar,
            type: 'extra',
            index: i
          })
        } else if (t[i] === q[i]) {
          // Characters match
          chars.push({
            char: textChar,
            type: 'match',
            index: i
          })
        } else {
          // Characters differ
          chars.push({
            char: textChar,
            type: 'diff',
            index: i
          })
        }
      }
    }

    const similarity = calculateSimilarity(query, text)
    return { chars, similarity }
  }

  /**
   * Advanced fuzzy highlighting using longest common subsequence
   */
  function highlightFuzzyDifferences(query: string, text: string): HighlightResult {
    if (!query || !text) {
      return {
        chars: text.split('').map((char, index) => ({
          char,
          type: 'extra' as const,
          index
        })),
        similarity: 0
      }
    }

    const q = query.toLowerCase()
    const t = text.toLowerCase()
    const chars: HighlightedChar[] = []

    // Find longest common subsequence to identify matching characters
    const lcs = findLCS(q, t)
    const matchingPositions = new Set<number>()

    // Mark positions that are part of the LCS
    let textPos = 0
    let queryPos = 0
    for (const char of lcs) {
      // Find next occurrence of this character in both strings
      while (textPos < t.length && t[textPos] !== char) textPos++
      while (queryPos < q.length && q[queryPos] !== char) queryPos++

      if (textPos < t.length && queryPos < q.length) {
        matchingPositions.add(textPos)
        textPos++
        queryPos++
      }
    }

    // Classify each character in the text
    for (let i = 0; i < text.length; i++) {
      if (matchingPositions.has(i)) {
        chars.push({
          char: text[i],
          type: 'match',
          index: i
        })
      } else if (i < query.length && t[i] !== q[i]) {
        chars.push({
          char: text[i],
          type: 'diff',
          index: i
        })
      } else {
        chars.push({
          char: text[i],
          type: 'extra',
          index: i
        })
      }
    }

    const similarity = calculateSimilarity(query, text)
    return { chars, similarity }
  }

  /**
   * Find Longest Common Subsequence
   */
  function findLCS(str1: string, str2: string): string {
    const m = str1.length
    const n = str2.length
    const dp = Array(m + 1).fill(null).map(() => Array(n + 1).fill(0))

    for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
        if (str1[i - 1] === str2[j - 1]) {
          dp[i][j] = dp[i - 1][j - 1] + 1
        } else {
          dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1])
        }
      }
    }

    // Reconstruct LCS
    let lcs = ''
    let i = m, j = n
    while (i > 0 && j > 0) {
      if (str1[i - 1] === str2[j - 1]) {
        lcs = str1[i - 1] + lcs
        i--
        j--
      } else if (dp[i - 1][j] > dp[i][j - 1]) {
        i--
      } else {
        j--
      }
    }

    return lcs
  }

  /**
   * Create a simple case-insensitive highlight for exact substring matches
   */
  function highlightSubstring(query: string, text: string): HighlightResult {
    if (!query || !text) {
      return {
        chars: text.split('').map((char, index) => ({
          char,
          type: 'extra' as const,
          index
        })),
        similarity: 0
      }
    }

    const lowerQuery = query.toLowerCase()
    const lowerText = text.toLowerCase()
    const chars: HighlightedChar[] = []

    let textIndex = 0
    while (textIndex < text.length) {
      const remainingText = lowerText.slice(textIndex)
      const matchIndex = remainingText.indexOf(lowerQuery)

      if (matchIndex === 0) {
        // Found a match at current position
        for (let i = 0; i < query.length; i++) {
          chars.push({
            char: text[textIndex + i],
            type: 'match',
            index: textIndex + i
          })
        }
        textIndex += query.length
      } else {
        // No match, mark as extra
        chars.push({
          char: text[textIndex],
          type: 'extra',
          index: textIndex
        })
        textIndex++
      }
    }

    const similarity = calculateSimilarity(query, text)
    return { chars, similarity }
  }

  /**
   * Get CSS classes for different highlight types
   */
  function getHighlightClasses(type: HighlightedChar['type']): string {
    switch (type) {
      case 'match':
        return 'bg-green-200 text-green-900 font-semibold'
      case 'diff':
        return 'bg-yellow-200 text-yellow-900 font-semibold'
      case 'extra':
        return 'bg-gray-100 text-gray-600'
      case 'missing':
        return 'bg-red-200 text-red-900 font-semibold'
      default:
        return ''
    }
  }

  return {
    highlightDifferences,
    highlightFuzzyDifferences,
    highlightSubstring,
    calculateSimilarity,
    getHighlightClasses
  }
}

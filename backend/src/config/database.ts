import { Pool } from 'pg';
import { Client } from '@elastic/elasticsearch';

// PostgreSQL configuration
export const pgPool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'parts_broker',
  password: process.env.DB_PASSWORD || 'postgres',
  port: parseInt(process.env.DB_PORT || '5432'),
});

// Elasticsearch configuration
export const esClient = new Client({
  node: process.env.ES_NODE || 'http://localhost:9200',
});

// Test database connections
export async function testConnections() {
  try {
    // Test PostgreSQL
    const pgResult = await pgPool.query('SELECT NOW()');
    console.log('✅ PostgreSQL connected:', pgResult.rows[0].now);

    // Test Elasticsearch
    const esResult = await esClient.ping();
    console.log('✅ Elasticsearch connected');

    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Initialize Elasticsearch indices
export async function initializeElasticsearch() {
  try {
    // Create parts index if it doesn't exist
    const indexExists = await esClient.indices.exists({ index: 'parts' });

    if (!indexExists) {
      await esClient.indices.create({
        index: 'parts',
        body: {
          mappings: {
            properties: {
              id: { type: 'keyword' },
              list_id: { type: 'keyword' },
              part_number: {
                type: 'text',
                analyzer: 'part_number_analyzer',
                fields: {
                  keyword: { type: 'keyword' },
                  ngram: {
                    type: 'text',
                    analyzer: 'ngram_analyzer'
                  },
                  fuzzy: {
                    type: 'text',
                    analyzer: 'fuzzy_analyzer'
                  }
                }
              },
              description: {
                type: 'text',
                analyzer: 'fuzzy_analyzer',
                fields: {
                  ngram: {
                    type: 'text',
                    analyzer: 'ngram_analyzer'
                  }
                }
              },
              manufacturer: {
                type: 'text',
                analyzer: 'fuzzy_analyzer',
                fields: {
                  keyword: { type: 'keyword' },
                  ngram: {
                    type: 'text',
                    analyzer: 'ngram_analyzer'
                  }
                }
              },
              quantity: { type: 'integer' },
              unit_price: { type: 'float' },
              condition: { type: 'keyword' },
              list_type: { type: 'keyword' },
              contact_id: { type: 'keyword' },
              created_at: { type: 'date' }
            }
          },
          settings: {
            analysis: {
              analyzer: {
                ngram_analyzer: {
                  type: 'custom',
                  tokenizer: 'standard',
                  filter: ['lowercase', 'ngram_filter']
                },
                fuzzy_analyzer: {
                  type: 'custom',
                  tokenizer: 'standard',
                  filter: ['lowercase', 'asciifolding', 'stop']
                },
                part_number_analyzer: {
                  type: 'custom',
                  tokenizer: 'keyword',
                  filter: ['lowercase', 'asciifolding']
                }
              },
              filter: {
                ngram_filter: {
                  type: 'ngram',
                  min_gram: 3,
                  max_gram: 6
                }
              }
            }
          }
        }
      });
      console.log('✅ Elasticsearch parts index created');
    }
  } catch (error) {
    console.error('❌ Elasticsearch initialization failed:', error);
  }
}
